import { supabase, type SessionLogRecord } from '@/lib/supabase';
import type { SessionLog } from '@/types';

export interface CreateSessionLogData {
  sessionId: string;
  sessionType: 'full_pipeline' | 'stt_test' | 'llm_test' | 'tts_test';
  inputAudio?: Blob;
  inputText?: string;
  systemPrompt?: string;
  toolsUsed?: string[];
}

export interface UpdateSessionLogData {
  status?: 'pending' | 'processing' | 'completed' | 'error';
  sttTranscript?: string;
  sttProvider?: string;
  sttModel?: string;
  sttConfidence?: number;
  sttProcessingTime?: number;
  llmResponse?: string;
  llmProvider?: string;
  llmModel?: string;
  llmProcessingTime?: number;
  llmTokensUsed?: number;
  ttsAudioUrl?: string;
  ttsProvider?: string;
  ttsModel?: string;
  ttsVoice?: string;
  ttsProcessingTime?: number;
  errorMessage?: string;
  processingMetrics?: Record<string, any>;
}

export interface ShareLinkOptions {
  expiresInHours?: number;
  includeAudio?: boolean;
}

class LoggingService {
  private static instance: LoggingService;
  private fallbackStorage: Map<string, SessionLogRecord> = new Map();
  private isSupabaseAvailable = false;

  private constructor() {
    this.checkSupabaseConnection();
  }

  public static getInstance(): LoggingService {
    if (!LoggingService.instance) {
      LoggingService.instance = new LoggingService();
    }
    return LoggingService.instance;
  }

  private async checkSupabaseConnection(): Promise<void> {
    try {
      const { error } = await supabase.from('session_logs').select('id').limit(1);
      this.isSupabaseAvailable = !error;
      
      if (error) {
        console.warn('Supabase not available, using local storage fallback:', error.message);
        this.loadFromLocalStorage();
      }
    } catch (error) {
      console.warn('Supabase connection failed, using local storage fallback:', error);
      this.isSupabaseAvailable = false;
      this.loadFromLocalStorage();
    }
  }

  private loadFromLocalStorage(): void {
    try {
      const stored = localStorage.getItem('voice_ai_session_logs');
      if (stored) {
        const logs = JSON.parse(stored);
        logs.forEach((log: SessionLogRecord) => {
          this.fallbackStorage.set(log.session_id, log);
        });
      }
    } catch (error) {
      console.error('Failed to load from localStorage:', error);
    }
  }

  private saveToLocalStorage(): void {
    try {
      const logs = Array.from(this.fallbackStorage.values());
      localStorage.setItem('voice_ai_session_logs', JSON.stringify(logs));
    } catch (error) {
      console.error('Failed to save to localStorage:', error);
    }
  }

  public async createSessionLog(data: CreateSessionLogData): Promise<string> {
    const sessionLog: Partial<SessionLogRecord> = {
      session_id: data.sessionId,
      session_type: data.sessionType,
      status: 'pending',
      input_text: data.inputText,
      system_prompt: data.systemPrompt,
      tools_used: data.toolsUsed,
      is_visible_to_public: true,
      is_featured: false,
    };

    // Handle audio upload if provided
    if (data.inputAudio) {
      try {
        const audioUrl = await this.uploadAudio(data.inputAudio, `input_${data.sessionId}`);
        sessionLog.input_audio_url = audioUrl;
      } catch (error) {
        console.error('Failed to upload input audio:', error);
      }
    }

    if (this.isSupabaseAvailable) {
      try {
        const { data: result, error } = await supabase
          .from('session_logs')
          .insert(sessionLog)
          .select('id')
          .single();

        if (error) throw error;
        return result.id;
      } catch (error) {
        console.error('Failed to create session log in Supabase:', error);
        // Fall back to local storage
        return this.createSessionLogLocal(sessionLog);
      }
    } else {
      return this.createSessionLogLocal(sessionLog);
    }
  }

  private createSessionLogLocal(sessionLog: Partial<SessionLogRecord>): string {
    const id = crypto.randomUUID();
    const fullLog: SessionLogRecord = {
      id,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_visible_to_public: true,
      is_featured: false,
      ...sessionLog,
    } as SessionLogRecord;

    this.fallbackStorage.set(sessionLog.session_id!, fullLog);
    this.saveToLocalStorage();
    return id;
  }

  public async updateSessionLog(sessionId: string, data: UpdateSessionLogData): Promise<void> {
    if (this.isSupabaseAvailable) {
      try {
        const { error } = await supabase
          .from('session_logs')
          .update({
            ...data,
            stt_transcript: data.sttTranscript,
            stt_provider: data.sttProvider,
            stt_model: data.sttModel,
            stt_confidence: data.sttConfidence,
            stt_processing_time: data.sttProcessingTime,
            llm_response: data.llmResponse,
            llm_provider: data.llmProvider,
            llm_model: data.llmModel,
            llm_processing_time: data.llmProcessingTime,
            llm_tokens_used: data.llmTokensUsed,
            tts_audio_url: data.ttsAudioUrl,
            tts_provider: data.ttsProvider,
            tts_model: data.ttsModel,
            tts_voice: data.ttsVoice,
            tts_processing_time: data.ttsProcessingTime,
            error_message: data.errorMessage,
            processing_metrics: data.processingMetrics,
          })
          .eq('session_id', sessionId);

        if (error) throw error;
      } catch (error) {
        console.error('Failed to update session log in Supabase:', error);
        this.updateSessionLogLocal(sessionId, data);
      }
    } else {
      this.updateSessionLogLocal(sessionId, data);
    }
  }

  private updateSessionLogLocal(sessionId: string, data: UpdateSessionLogData): void {
    const existing = this.fallbackStorage.get(sessionId);
    if (existing) {
      const updated = {
        ...existing,
        ...data,
        stt_transcript: data.sttTranscript,
        stt_provider: data.sttProvider,
        stt_model: data.sttModel,
        stt_confidence: data.sttConfidence,
        stt_processing_time: data.sttProcessingTime,
        llm_response: data.llmResponse,
        llm_provider: data.llmProvider,
        llm_model: data.llmModel,
        llm_processing_time: data.llmProcessingTime,
        llm_tokens_used: data.llmTokensUsed,
        tts_audio_url: data.ttsAudioUrl,
        tts_provider: data.ttsProvider,
        tts_model: data.ttsModel,
        tts_voice: data.ttsVoice,
        tts_processing_time: data.ttsProcessingTime,
        error_message: data.errorMessage,
        processing_metrics: data.processingMetrics,
        updated_at: new Date().toISOString(),
      };
      this.fallbackStorage.set(sessionId, updated);
      this.saveToLocalStorage();
    }
  }

  public async getSessionLogs(limit = 50, offset = 0): Promise<SessionLogRecord[]> {
    if (this.isSupabaseAvailable) {
      try {
        const { data, error } = await supabase
          .from('session_logs')
          .select('*')
          .eq('is_visible_to_public', true)
          .order('created_at', { ascending: false })
          .range(offset, offset + limit - 1);

        if (error) throw error;
        return data || [];
      } catch (error) {
        console.error('Failed to fetch session logs from Supabase:', error);
        return this.getSessionLogsLocal(limit, offset);
      }
    } else {
      return this.getSessionLogsLocal(limit, offset);
    }
  }

  private getSessionLogsLocal(limit: number, offset: number): SessionLogRecord[] {
    const logs = Array.from(this.fallbackStorage.values())
      .filter(log => log.is_visible_to_public)
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .slice(offset, offset + limit);
    return logs;
  }

  public async generateShareLink(sessionId: string, options: ShareLinkOptions = {}): Promise<string> {
    const { expiresInHours = 24 } = options;
    const shareToken = crypto.randomUUID();
    const expiresAt = new Date(Date.now() + expiresInHours * 60 * 60 * 1000).toISOString();

    if (this.isSupabaseAvailable) {
      try {
        const { error } = await supabase
          .from('session_logs')
          .update({
            share_token: shareToken,
            share_expires_at: expiresAt,
          })
          .eq('session_id', sessionId);

        if (error) throw error;
      } catch (error) {
        console.error('Failed to generate share link in Supabase:', error);
        this.generateShareLinkLocal(sessionId, shareToken, expiresAt);
      }
    } else {
      this.generateShareLinkLocal(sessionId, shareToken, expiresAt);
    }

    return `${window.location.origin}/share/${shareToken}`;
  }

  private generateShareLinkLocal(sessionId: string, shareToken: string, expiresAt: string): void {
    const existing = this.fallbackStorage.get(sessionId);
    if (existing) {
      existing.share_token = shareToken;
      existing.share_expires_at = expiresAt;
      existing.updated_at = new Date().toISOString();
      this.fallbackStorage.set(sessionId, existing);
      this.saveToLocalStorage();
    }
  }

  private async uploadAudio(audioBlob: Blob, fileName: string): Promise<string> {
    if (!this.isSupabaseAvailable) {
      // For local fallback, we'll create a blob URL
      return URL.createObjectURL(audioBlob);
    }

    try {
      const { data, error } = await supabase.storage
        .from('audio-files')
        .upload(`${fileName}.webm`, audioBlob, {
          contentType: 'audio/webm',
          upsert: true,
        });

      if (error) throw error;

      const { data: urlData } = supabase.storage
        .from('audio-files')
        .getPublicUrl(data.path);

      return urlData.publicUrl;
    } catch (error) {
      console.error('Failed to upload audio:', error);
      return URL.createObjectURL(audioBlob);
    }
  }

  // Admin methods
  public async setSessionVisibility(sessionId: string, isVisible: boolean): Promise<void> {
    if (this.isSupabaseAvailable) {
      try {
        const { error } = await supabase
          .from('session_logs')
          .update({ is_visible_to_public: isVisible })
          .eq('session_id', sessionId);

        if (error) throw error;
      } catch (error) {
        console.error('Failed to update session visibility:', error);
      }
    }

    // Update local storage as well
    const existing = this.fallbackStorage.get(sessionId);
    if (existing) {
      existing.is_visible_to_public = isVisible;
      existing.updated_at = new Date().toISOString();
      this.fallbackStorage.set(sessionId, existing);
      this.saveToLocalStorage();
    }
  }

  public async getAllSessionLogs(includeHidden = false): Promise<SessionLogRecord[]> {
    if (this.isSupabaseAvailable) {
      try {
        let query = supabase.from('session_logs').select('*');
        
        if (!includeHidden) {
          query = query.eq('is_visible_to_public', true);
        }

        const { data, error } = await query.order('created_at', { ascending: false });

        if (error) throw error;
        return data || [];
      } catch (error) {
        console.error('Failed to fetch all session logs:', error);
        return this.getAllSessionLogsLocal(includeHidden);
      }
    } else {
      return this.getAllSessionLogsLocal(includeHidden);
    }
  }

  private getAllSessionLogsLocal(includeHidden: boolean): SessionLogRecord[] {
    let logs = Array.from(this.fallbackStorage.values());
    
    if (!includeHidden) {
      logs = logs.filter(log => log.is_visible_to_public);
    }

    return logs.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  }
}

export default LoggingService;
