import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import LoginForm from '@/components/auth/LoginForm';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Info } from 'lucide-react';

const AdminLogin: React.FC = () => {
  const { state } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Get the intended destination or default to admin dashboard
  const from = (location.state as any)?.from || '/admin';
  const isSessionExpired = (location.state as any)?.expired;

  // Redirect if already authenticated
  useEffect(() => {
    if (state.isAuthenticated && !state.isLoading) {
      navigate(from, { replace: true });
    }
  }, [state.isAuthenticated, state.isLoading, navigate, from]);

  const handleLoginSuccess = () => {
    navigate(from, { replace: true });
  };

  // Don't render login form if already authenticated
  if (state.isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      {isSessionExpired && (
        <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-md px-4">
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              Your session has expired. Please log in again.
            </AlertDescription>
          </Alert>
        </div>
      )}
      
      <LoginForm 
        onSuccess={handleLoginSuccess}
        redirectTo={from}
      />
    </div>
  );
};

export default AdminLogin;
