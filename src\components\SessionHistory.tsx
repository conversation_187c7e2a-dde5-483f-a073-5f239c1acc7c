
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON>Title } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useSessionLogs } from '@/hooks/useSessionLogs';
import { useAuth } from '@/context/AuthContext';
import {
  Clock,
  Download,
  Share2,
  Eye,
  EyeOff,
  ExternalLink,
  Mic,
  Brain,
  Volume2,
  Zap
} from 'lucide-react';
import type { SessionLogRecord } from '@/lib/supabase';

interface SessionHistoryProps {
  sessions?: any[]; // Legacy prop for compatibility
  onSessionSelect?: (session: any) => void; // Legacy prop for compatibility
  showAdminControls?: boolean;
}

const SessionHistory: React.FC<SessionHistoryProps> = ({
  sessions: legacySessions,
  onSessionSelect,
  showAdminControls = false
}) => {
  const { state: authState } = useAuth();
  const { sessionLogs, isLoading, error, refreshLogs, generateShareLink, setSessionVisibility } = useSessionLogs(showAdminControls);
  const { toast } = useToast();
  const [generatingShareLink, setGeneratingShareLink] = useState<string | null>(null);

  // Use persistent logs or fall back to legacy sessions
  const sessions = sessionLogs.length > 0 ? sessionLogs : (legacySessions || []);

  const getSessionTypeIcon = (sessionType: string) => {
    switch (sessionType) {
      case 'full_pipeline': return <Zap className="w-4 h-4" />;
      case 'stt_test': return <Mic className="w-4 h-4" />;
      case 'llm_test': return <Brain className="w-4 h-4" />;
      case 'tts_test': return <Volume2 className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500';
      case 'error': return 'bg-red-500';
      case 'processing': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  const handleShareSession = async (session: SessionLogRecord) => {
    try {
      setGeneratingShareLink(session.session_id);
      const shareUrl = await generateShareLink(session.session_id);

      await navigator.clipboard.writeText(shareUrl);
      toast({
        title: "Share Link Copied",
        description: "The shareable link has been copied to your clipboard"
      });
    } catch (error) {
      toast({
        title: "Failed to Generate Share Link",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive"
      });
    } finally {
      setGeneratingShareLink(null);
    }
  };

  const handleToggleVisibility = async (session: SessionLogRecord) => {
    try {
      await setSessionVisibility(session.session_id, !session.is_visible_to_public);
      toast({
        title: "Session Updated",
        description: `Session is now ${!session.is_visible_to_public ? 'visible' : 'hidden'} to public users`
      });
    } catch (error) {
      toast({
        title: "Failed to Update Session",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive"
      });
    }
  };

  if (isLoading) {
    return (
      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-lg">
        <CardContent className="p-8 text-center">
          <div className="animate-spin w-8 h-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-4" />
          <div className="text-muted-foreground">Loading session history...</div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-lg">
        <CardContent className="p-8 text-center">
          <div className="text-red-500 mb-4">Error loading sessions</div>
          <div className="text-sm text-muted-foreground mb-4">{error}</div>
          <Button onClick={refreshLogs} variant="outline">
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (sessions.length === 0) {
    return (
      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-lg">
        <CardContent className="p-8 text-center">
          <Clock className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <div className="text-muted-foreground">No session history</div>
          <div className="text-sm text-muted-foreground mt-2">
            Previous sessions will appear here
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-lg">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-lg flex items-center gap-2">
          <Clock className="w-5 h-5" />
          Session History
        </CardTitle>
        <div className="flex items-center gap-2">
          <Badge variant="outline">{sessions.length} sessions</Badge>
          {showAdminControls && (
            <Button onClick={refreshLogs} variant="outline" size="sm">
              Refresh
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {sessions.map((session, index) => {
            const isSessionLogRecord = 'session_id' in session;
            const sessionId = isSessionLogRecord ? session.session_id : session.id;
            const timestamp = isSessionLogRecord ? session.created_at : session.timestamp;
            const sessionType = isSessionLogRecord ? session.session_type : 'full_pipeline';
            const status = isSessionLogRecord ? session.status : session.status || 'completed';

            return (
              <div
                key={sessionId}
                className="flex items-center justify-between p-3 bg-white/50 rounded-lg border border-white/30 hover:border-white/50 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <div className={`w-2 h-2 rounded-full ${getStatusColor(status)}`} />
                  <div className="flex items-center gap-2 text-primary">
                    {getSessionTypeIcon(sessionType)}
                  </div>
                  <div>
                    <div className="font-medium text-sm">
                      {new Date(timestamp).toLocaleString()}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {sessionType.replace('_', ' ').toUpperCase()} • {status.toUpperCase()}
                      {isSessionLogRecord && session.stt_processing_time && session.llm_processing_time && session.tts_processing_time && (
                        <> • {((session.stt_processing_time + session.llm_processing_time + session.tts_processing_time) / 1000).toFixed(1)}s</>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onSessionSelect?.(session)}
                  >
                    <ExternalLink className="w-3 h-3 mr-1" />
                    View
                  </Button>

                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleShareSession(session as SessionLogRecord)}
                    disabled={generatingShareLink === sessionId}
                  >
                    {generatingShareLink === sessionId ? (
                      <div className="w-3 h-3 animate-spin border border-current border-t-transparent rounded-full" />
                    ) : (
                      <Share2 className="w-3 h-3" />
                    )}
                  </Button>

                  {showAdminControls && authState.isAuthenticated && isSessionLogRecord && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleToggleVisibility(session as SessionLogRecord)}
                      className={session.is_visible_to_public ? "text-green-600" : "text-red-600"}
                    >
                      {session.is_visible_to_public ? (
                        <Eye className="w-3 h-3" />
                      ) : (
                        <EyeOff className="w-3 h-3" />
                      )}
                    </Button>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {sessions.length > 0 && (
          <div className="mt-6 pt-4 border-t border-white/20">
            <Button variant="outline" className="w-full">
              <Download className="w-4 h-4 mr-2" />
              Export All Sessions
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SessionHistory;
