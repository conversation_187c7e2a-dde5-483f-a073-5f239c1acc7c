/**
 * API Proxy Service
 * Handles API calls through Netlify Functions in production or direct calls in development
 */

import ConfigService from './configService';

export interface STTRequest {
  audio: string; // base64 encoded audio
  model?: string;
  language?: string;
  enhanced?: boolean;
}

export interface LLMRequest {
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  model?: string;
  temperature?: number;
  max_tokens?: number;
  tools?: any[];
}

export interface TTSRequest {
  text: string;
  model?: string;
  voice?: string;
  voice_id?: string;
  voice_settings?: {
    stability: number;
    similarity_boost: number;
    style?: number;
    use_speaker_boost?: boolean;
  };
}

class ApiProxyService {
  private static instance: ApiProxyService;
  private configService: ConfigService;

  private constructor() {
    this.configService = ConfigService.getInstance();
  }

  public static getInstance(): ApiProxyService {
    if (!ApiProxyService.instance) {
      ApiProxyService.instance = new ApiProxyService();
    }
    return ApiProxyService.instance;
  }

  private getBaseUrl(): string {
    const config = this.configService.getConfig();
    return config.apiBaseUrl || '/.netlify/functions';
  }

  private isProduction(): boolean {
    const config = this.configService.getConfig();
    return config.environment === 'production';
  }

  /**
   * Speech-to-Text API call
   */
  public async callSTT(provider: string, request: STTRequest): Promise<any> {
    const baseUrl = this.getBaseUrl();
    
    try {
      const response = await fetch(`${baseUrl}/deepgram-proxy`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...request,
          provider, // Pass provider info to function
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `STT API error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('STT API call failed:', error);
      throw error;
    }
  }

  /**
   * Language Model API call
   */
  public async callLLM(provider: string, request: LLMRequest): Promise<any> {
    const baseUrl = this.getBaseUrl();
    
    try {
      let endpoint = 'openai-proxy';
      
      // Route to appropriate proxy function based on provider
      switch (provider) {
        case 'openai':
          endpoint = 'openai-proxy';
          break;
        case 'anthropic':
          endpoint = 'anthropic-proxy';
          break;
        case 'google':
          endpoint = 'google-proxy';
          break;
        default:
          endpoint = 'openai-proxy';
      }

      const response = await fetch(`${baseUrl}/${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...request,
          provider,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `LLM API error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('LLM API call failed:', error);
      throw error;
    }
  }

  /**
   * Text-to-Speech API call
   */
  public async callTTS(provider: string, request: TTSRequest): Promise<any> {
    const baseUrl = this.getBaseUrl();
    
    try {
      let endpoint = 'elevenlabs-proxy';
      
      // Route to appropriate proxy function based on provider
      switch (provider) {
        case 'elevenlabs':
          endpoint = 'elevenlabs-proxy';
          break;
        case 'openai':
          endpoint = 'openai-proxy/tts';
          break;
        case 'google':
          endpoint = 'google-tts-proxy';
          break;
        case 'azure':
          endpoint = 'azure-tts-proxy';
          break;
        default:
          endpoint = 'elevenlabs-proxy';
      }

      const response = await fetch(`${baseUrl}/${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...request,
          provider,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `TTS API error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('TTS API call failed:', error);
      throw error;
    }
  }

  /**
   * Get available voices for a TTS provider
   */
  public async getVoices(provider: string): Promise<any> {
    const baseUrl = this.getBaseUrl();
    
    try {
      let endpoint = 'elevenlabs-proxy/voices';
      
      switch (provider) {
        case 'elevenlabs':
          endpoint = 'elevenlabs-proxy/voices';
          break;
        case 'google':
          endpoint = 'google-tts-proxy/voices';
          break;
        case 'azure':
          endpoint = 'azure-tts-proxy/voices';
          break;
        default:
          // For providers like OpenAI that have static voice lists
          return this.getStaticVoices(provider);
      }

      const response = await fetch(`${baseUrl}/${endpoint}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Voices API error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Voices API call failed:', error);
      // Fallback to static voices if API call fails
      return this.getStaticVoices(provider);
    }
  }

  /**
   * Get available models for a provider
   */
  public async getModels(provider: string, category: 'stt' | 'llm' | 'tts'): Promise<any> {
    const baseUrl = this.getBaseUrl();
    
    try {
      let endpoint = `${provider}-proxy/models`;
      
      const response = await fetch(`${baseUrl}/${endpoint}?category=${category}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        // Fallback to static model registry if API call fails
        console.warn(`Models API call failed for ${provider}, using static registry`);
        return null;
      }

      return await response.json();
    } catch (error) {
      console.error('Models API call failed:', error);
      return null;
    }
  }

  /**
   * Fallback static voices for providers that don't have dynamic voice APIs
   */
  private getStaticVoices(provider: string): any {
    const staticVoices = {
      openai: {
        voices: [
          { id: 'alloy', name: 'Alloy', gender: 'neutral' },
          { id: 'echo', name: 'Echo', gender: 'male' },
          { id: 'fable', name: 'Fable', gender: 'male' },
          { id: 'onyx', name: 'Onyx', gender: 'male' },
          { id: 'nova', name: 'Nova', gender: 'female' },
          { id: 'shimmer', name: 'Shimmer', gender: 'female' },
        ]
      }
    };

    return staticVoices[provider as keyof typeof staticVoices] || { voices: [] };
  }

  /**
   * Health check for API proxy
   */
  public async healthCheck(): Promise<boolean> {
    const baseUrl = this.getBaseUrl();
    
    try {
      const response = await fetch(`${baseUrl}/health`, {
        method: 'GET',
      });
      
      return response.ok;
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  }
}

export default ApiProxyService;
