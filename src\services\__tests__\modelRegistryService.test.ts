import { describe, it, expect, beforeEach } from 'vitest';
import ModelRegistryService from '../modelRegistryService';

describe('ModelRegistryService', () => {
  let modelRegistry: ModelRegistryService;

  beforeEach(() => {
    modelRegistry = ModelRegistryService.getInstance();
  });

  describe('Singleton Pattern', () => {
    it('should return the same instance', () => {
      const instance1 = ModelRegistryService.getInstance();
      const instance2 = ModelRegistryService.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('STT Models', () => {
    it('should return STT models', () => {
      const sttModels = modelRegistry.getSTTModels();
      expect(sttModels).toBeDefined();
      expect(Array.isArray(sttModels)).toBe(true);
      expect(sttModels.length).toBeGreaterThan(0);
    });

    it('should include Deepgram Nova 2 model', () => {
      const sttModels = modelRegistry.getSTTModels();
      const nova2 = sttModels.find(model => model.id === 'nova-2');
      expect(nova2).toBeDefined();
      expect(nova2?.provider).toBe('deepgram');
      expect(nova2?.category).toBe('stt');
      expect(nova2?.isRecommended).toBe(true);
    });

    it('should include OpenAI Whisper model', () => {
      const sttModels = modelRegistry.getSTTModels();
      const whisper = sttModels.find(model => model.id === 'whisper-1');
      expect(whisper).toBeDefined();
      expect(whisper?.provider).toBe('openai');
      expect(whisper?.category).toBe('stt');
    });
  });

  describe('LLM Models', () => {
    it('should return LLM models', () => {
      const llmModels = modelRegistry.getLLMModels();
      expect(llmModels).toBeDefined();
      expect(Array.isArray(llmModels)).toBe(true);
      expect(llmModels.length).toBeGreaterThan(0);
    });

    it('should include GPT-4o model', () => {
      const llmModels = modelRegistry.getLLMModels();
      const gpt4o = llmModels.find(model => model.id === 'gpt-4o');
      expect(gpt4o).toBeDefined();
      expect(gpt4o?.provider).toBe('openai');
      expect(gpt4o?.category).toBe('llm');
      expect(gpt4o?.isRecommended).toBe(true);
      expect(gpt4o?.isNew).toBe(true);
    });

    it('should include Claude 3.5 Sonnet model', () => {
      const llmModels = modelRegistry.getLLMModels();
      const claude35 = llmModels.find(model => model.id === 'claude-3-5-sonnet-20241022');
      expect(claude35).toBeDefined();
      expect(claude35?.provider).toBe('anthropic');
      expect(claude35?.category).toBe('llm');
      expect(claude35?.isRecommended).toBe(true);
    });
  });

  describe('TTS Models', () => {
    it('should return TTS models', () => {
      const ttsModels = modelRegistry.getTTSModels();
      expect(ttsModels).toBeDefined();
      expect(Array.isArray(ttsModels)).toBe(true);
      expect(ttsModels.length).toBeGreaterThan(0);
    });

    it('should include ElevenLabs Multilingual v2 model', () => {
      const ttsModels = modelRegistry.getTTSModels();
      const elevenMulti = ttsModels.find(model => model.id === 'eleven_multilingual_v2');
      expect(elevenMulti).toBeDefined();
      expect(elevenMulti?.provider).toBe('elevenlabs');
      expect(elevenMulti?.category).toBe('tts');
      expect(elevenMulti?.isRecommended).toBe(true);
    });

    it('should include OpenAI TTS-1 HD model', () => {
      const ttsModels = modelRegistry.getTTSModels();
      const openaiTTS = ttsModels.find(model => model.id === 'tts-1-hd');
      expect(openaiTTS).toBeDefined();
      expect(openaiTTS?.provider).toBe('openai');
      expect(openaiTTS?.category).toBe('tts');
    });
  });

  describe('Voices', () => {
    it('should return voices', () => {
      const voices = modelRegistry.getVoices();
      expect(voices).toBeDefined();
      expect(Array.isArray(voices)).toBe(true);
      expect(voices.length).toBeGreaterThan(0);
    });

    it('should include ElevenLabs Aria voice', () => {
      const voices = modelRegistry.getVoices();
      const aria = voices.find(voice => voice.id === '9BWtsMINqrJLrRacOk9x');
      expect(aria).toBeDefined();
      expect(aria?.name).toBe('Aria');
      expect(aria?.provider).toBe('elevenlabs');
      expect(aria?.gender).toBe('female');
      expect(aria?.isRecommended).toBe(true);
    });

    it('should include OpenAI voices', () => {
      const voices = modelRegistry.getVoices();
      const openaiVoices = voices.filter(voice => voice.provider === 'openai');
      expect(openaiVoices.length).toBeGreaterThan(0);
      
      const alloy = openaiVoices.find(voice => voice.id === 'alloy');
      expect(alloy).toBeDefined();
      expect(alloy?.name).toBe('Alloy');
    });
  });

  describe('Utility Methods', () => {
    it('should filter models by provider', () => {
      const openaiModels = modelRegistry.getModelsByProvider('openai');
      expect(openaiModels.length).toBeGreaterThan(0);
      openaiModels.forEach(model => {
        expect(model.provider).toBe('openai');
      });
    });

    it('should filter models by provider and category', () => {
      const openaiLLMs = modelRegistry.getModelsByProvider('openai', 'llm');
      expect(openaiLLMs.length).toBeGreaterThan(0);
      openaiLLMs.forEach(model => {
        expect(model.provider).toBe('openai');
        expect(model.category).toBe('llm');
      });
    });

    it('should return recommended models', () => {
      const recommendedModels = modelRegistry.getRecommendedModels();
      expect(recommendedModels.length).toBeGreaterThan(0);
      recommendedModels.forEach(model => {
        expect(model.isRecommended).toBe(true);
      });
    });

    it('should return recommended models by category', () => {
      const recommendedSTT = modelRegistry.getRecommendedModels('stt');
      expect(recommendedSTT.length).toBeGreaterThan(0);
      recommendedSTT.forEach(model => {
        expect(model.category).toBe('stt');
        expect(model.isRecommended).toBe(true);
      });
    });

    it('should filter voices by provider', () => {
      const elevenlabsVoices = modelRegistry.getVoicesByProvider('elevenlabs');
      expect(elevenlabsVoices.length).toBeGreaterThan(0);
      elevenlabsVoices.forEach(voice => {
        expect(voice.provider).toBe('elevenlabs');
      });
    });

    it('should return available providers', () => {
      const allProviders = modelRegistry.getAvailableProviders();
      expect(allProviders).toContain('openai');
      expect(allProviders).toContain('deepgram');
      expect(allProviders).toContain('elevenlabs');
      expect(allProviders).toContain('anthropic');
      expect(allProviders).toContain('google');
    });

    it('should return available providers by category', () => {
      const sttProviders = modelRegistry.getAvailableProviders('stt');
      expect(sttProviders).toContain('deepgram');
      expect(sttProviders).toContain('openai');
      expect(sttProviders).toContain('assemblyai');
      expect(sttProviders).toContain('google');
    });
  });

  describe('Model Properties', () => {
    it('should have required properties for all models', () => {
      const allModels = [
        ...modelRegistry.getSTTModels(),
        ...modelRegistry.getLLMModels(),
        ...modelRegistry.getTTSModels()
      ];

      allModels.forEach(model => {
        expect(model.id).toBeDefined();
        expect(model.name).toBeDefined();
        expect(model.description).toBeDefined();
        expect(model.provider).toBeDefined();
        expect(model.category).toBeDefined();
        expect(['stt', 'llm', 'tts']).toContain(model.category);
      });
    });

    it('should have valid pricing tiers', () => {
      const allModels = [
        ...modelRegistry.getSTTModels(),
        ...modelRegistry.getLLMModels(),
        ...modelRegistry.getTTSModels()
      ];

      allModels.forEach(model => {
        if (model.pricing) {
          expect(['free', 'paid', 'premium']).toContain(model.pricing);
        }
      });
    });
  });
});
