# Voice AI Sandbox 🎙️🤖

A comprehensive two-tier testing environment for voice AI pipelines with **secure admin controls**, **persistent logging**, and **public sharing capabilities**. Built with React, TypeScript, Supabase, and deployed on Netlify.

## 🏗️ Two-Tier Architecture

### 🌐 **Public End-User Interface** (`/`)
Clean, user-friendly interface for testing voice AI capabilities:
- **Full Pipeline**: Complete STT → LLM → TTS workflow testing
- **STT Testing**: Speech-to-text testing with multiple providers/models
- **LLM Testing**: Language model testing with multiple providers/models
- **TTS Testing**: Text-to-speech testing with multiple providers/models
- **Session Logs**: View and listen to all test session history

### 🔐 **Protected Developer Interface** (`/admin/*`)
Secure admin control panel with authentication (`22` / `I'mTheH<PERSON>man!`):
- System prompt editing and management
- Provider/model configuration and settings
- Tools and capabilities management
- Session visibility controls and analytics
- Advanced system monitoring and controls

### 📊 **Persistent Logging System**
Robust session tracking with Supa<PERSON> backend:
- All sessions automatically logged with detailed metrics
- Shareable links for individual sessions (`/share/:token`)
- Admin controls to hide/show sessions from public view
- Fallback to localStorage when Supabase unavailable

## ✨ Key Features

### 🔊 Multi-Provider Voice AI Testing
- **Speech-to-Text**: Deepgram, OpenAI Whisper, AssemblyAI, Google Speech
- **Language Models**: OpenAI GPT-4o/4/3.5, Anthropic Claude 3.5, Google Gemini 1.5
- **Text-to-Speech**: ElevenLabs, OpenAI TTS, Google Cloud TTS, Azure Speech

### 🔐 Secure Two-Tier Access
- **Public Interface**: User-friendly testing environment for end users
- **Admin Interface**: Protected control panel with full system management
- **Session-based Authentication**: Secure login with automatic session management
- **Route Protection**: Proper access controls for sensitive areas

### 📊 Persistent Session Management
- **Supabase Integration**: Robust backend for session persistence and analytics
- **Shareable Links**: Generate public links for individual test sessions
- **Admin Controls**: Hide/show sessions, manage visibility, and export data
- **Fallback Storage**: Automatic localStorage fallback when Supabase unavailable

### 🎯 Comprehensive Model Selection
- **ALL available models** from each provider
- **Smart filtering** by capabilities, pricing, and recommendations
- **Voice selection** with gender, accent, and language options
- **Real-time model switching** without page reload

### 🔐 Production-Ready Security
- **Secure API key management** via Netlify Functions
- **Server-side proxy** for all API calls
- **No API keys exposed** to client-side code
- **Environment-based configuration**

### 🚀 Advanced Testing Capabilities
- **Full Pipeline Testing**: End-to-end voice conversation simulation
- **Individual Component Testing**: Test STT, LLM, and TTS separately
- **Tool Integration**: Function calling and tool usage testing
- **Real-time Processing**: Live audio recording and streaming
- **Performance Metrics**: Detailed latency and quality measurements

## 🏗️ Tech Stack

- **Frontend**: React 18, TypeScript, Tailwind CSS
- **UI Components**: Radix UI, shadcn/ui
- **State Management**: React Context + useReducer (following user preferences)
- **Testing**: Vitest, React Testing Library (Jest + RTL as preferred)
- **Build Tool**: Vite
- **Deployment**: Netlify with Functions
- **Security**: Server-side API proxying

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- npm or yarn
- API keys for the services you want to test

### Local Development

1. **Clone and install**:
```bash
git clone <repository-url>
cd voice-ai-sandbox
npm install
```

2. **Set up environment variables**:
```bash
cp .env.example .env.local
```

3. **Add your API keys to `.env.local`**:
```env
# For local development only
VITE_DEEPGRAM_API_KEY=your_deepgram_key
VITE_OPENAI_API_KEY=your_openai_key
VITE_ELEVENLABS_API_KEY=your_elevenlabs_key
```

4. **Start development server**:
```bash
npm run dev
```

### 🌐 Production Deployment

For secure production deployment with your own API keys, see **[DEPLOYMENT.md](./DEPLOYMENT.md)** for the complete guide.

**Quick Deploy to Netlify:**
1. Push code to GitHub/GitLab
2. Connect to Netlify
3. Add API keys as environment variables (server-side)
4. Deploy automatically with secure API proxying

## 🎯 Usage Guide

### 🔍 Model Selection

The new **ModelSelector** component provides:

- **Provider Selection**: Choose from all supported providers
- **Model Filtering**: Filter by recommended, new, pricing tier
- **Search Functionality**: Find models by name or capabilities
- **Detailed Information**: See model capabilities, context windows, pricing
- **Voice Selection**: For TTS, browse all available voices with previews

### 🎙️ Individual Component Testing

#### Speech-to-Text Testing
- Record audio or upload files
- Test with **15+ models** across 4 providers
- Compare accuracy and latency
- Support for multiple languages

#### Language Model Testing
- Test with **12+ latest models** including GPT-4o, Claude 3.5 Sonnet
- Function calling and tool integration
- Custom system prompts
- Token usage tracking

#### Text-to-Speech Testing
- **40+ voices** across multiple providers
- Real-time voice switching
- Custom voice ID support
- Audio quality comparison

### 🔄 Full Pipeline Testing

Complete voice conversation simulation:
1. **Record** your voice input
2. **Transcribe** with selected STT model
3. **Process** with chosen LLM
4. **Generate** speech with selected TTS voice
5. **Play back** the AI response

### 🛠️ Tool Integration

Advanced function calling testing:
- Define custom tools and functions
- Test LLM tool usage capabilities
- Monitor execution and results
- Debug tool call parameters

## 🔐 Security Architecture

### Production Security (Recommended)
```
Client → Netlify Function → AI Provider API
         (Your API Key)
```
- ✅ API keys stored server-side only
- ✅ Secure environment variables
- ✅ No client exposure

### Development Security
```
Client → AI Provider API
(Local API Key)
```
- ⚠️ For development/testing only
- ⚠️ API keys in local environment

## 🧪 Testing

```bash
# Run all tests
npm test

# Interactive test UI
npm run test:ui

# Coverage report
npm run test:coverage

# Watch mode
npm run test:watch
```

## 📊 Available Models

### Speech-to-Text (STT)
- **Deepgram**: Nova 2 ⭐, Nova, Enhanced, Base
- **OpenAI**: Whisper ⭐
- **AssemblyAI**: Best ⭐, Nano
- **Google**: Latest Long, Latest Short ⭐, Chirp 🆕

### Language Models (LLM)
- **OpenAI**: GPT-4o 🆕⭐, GPT-4 Turbo ⭐, GPT-4, GPT-3.5 Turbo
- **Anthropic**: Claude 3.5 Sonnet 🆕⭐, Claude 3 Opus, Sonnet ⭐, Haiku
- **Google**: Gemini 1.5 Pro 🆕⭐, Gemini 1.5 Flash ⭐, Gemini Pro

### Text-to-Speech (TTS)
- **ElevenLabs**: Multilingual v2 ⭐, Turbo v2.5 🆕⭐, Turbo v2, Monolingual v1
- **OpenAI**: TTS-1 HD ⭐, TTS-1
- **Google**: Neural2 🆕⭐, WaveNet, Standard
- **Azure**: Neural ⭐, Standard

*⭐ = Recommended, 🆕 = New*

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

---

**Ready to deploy?** Check out [DEPLOYMENT.md](./DEPLOYMENT.md) for the complete production deployment guide! 🚀
