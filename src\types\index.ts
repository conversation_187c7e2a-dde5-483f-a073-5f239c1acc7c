/**
 * Comprehensive type definitions for the Voice AI Testing Framework
 */

// Audio processing types
export interface AudioConfig {
  sampleRate: number;
  channels: number;
  bitDepth: number;
}

export interface AudioRecordingState {
  isRecording: boolean;
  duration: number;
  audioLevel: number;
  audioBlob: Blob | null;
}

// Processing pipeline types
export type ProcessingStage = 'idle' | 'recording' | 'processing' | 'completed' | 'error';

export interface ProcessingMetrics {
  sttLatency: number;
  llmProcessing: number;
  ttsGeneration: number;
  totalTime: number;
}

// Session and logging types
export interface SessionLog {
  id: string;
  timestamp: string;
  inputAudio: Blob | null;
  transcript: string;
  gptResponse: string;
  ttsAudio: Blob | null;
  status: 'pending' | 'processing' | 'completed' | 'error';
  metrics?: ProcessingMetrics;
  error?: string;
}

// API provider types
export type STTProvider = 'deepgram' | 'openai' | 'assemblyai' | 'google';
export type LLMProvider = 'openai' | 'anthropic' | 'google' | 'cohere';
export type TTSProvider = 'elevenlabs' | 'openai' | 'google' | 'azure';

export interface STTModel {
  provider: STTProvider;
  model: string;
  language?: string;
  enhanced?: boolean;
}

export interface LLMModel {
  provider: LLMProvider;
  model: string;
  maxTokens?: number;
  temperature?: number;
}

export interface TTSModel {
  provider: TTSProvider;
  model: string;
  voice: string;
  speed?: number;
  stability?: number;
}

// Tool system types
export interface ToolParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  required: boolean;
  description?: string;
  default?: unknown;
}

export interface Tool {
  id: string;
  name: string;
  description: string;
  webhookUrl: string;
  parameters: Record<string, 'string' | 'number' | 'boolean' | 'array' | 'object'>;
  enabled: boolean;
  version?: string;
  timeout?: number;
}

export interface ToolCall {
  toolId: string;
  toolName: string;
  parameters: Record<string, unknown>;
  timestamp: string;
  status: 'pending' | 'success' | 'error';
  response?: unknown;
  error?: string;
  duration?: number;
}

// Voice configuration types
export interface Voice {
  id: string;
  name: string;
  provider: TTSProvider;
  language?: string;
  gender?: 'male' | 'female' | 'neutral';
  accent?: string;
}

// Configuration types
export interface ApiConfiguration {
  deepgramApiKey?: string;
  openaiApiKey?: string;
  elevenlabsApiKey?: string;
  apiBaseUrl?: string;
  environment?: 'development' | 'staging' | 'production';
}

export interface AppConfiguration {
  api: ApiConfiguration;
  defaultModels: {
    stt: STTModel;
    llm: LLMModel;
    tts: TTSModel;
  };
  ui: {
    theme: 'light' | 'dark' | 'system';
    language: string;
  };
}

// Authentication types
export interface AuthState {
  isAuthenticated: boolean;
  user: AuthUser | null;
  sessionExpiry: string | null;
  loginAttempts: number;
  isLoading: boolean;
}

export interface AuthUser {
  username: string;
  role: 'admin';
  loginTime: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export type AuthAction =
  | { type: 'LOGIN_START' }
  | { type: 'LOGIN_SUCCESS'; payload: { user: AuthUser; sessionExpiry: string } }
  | { type: 'LOGIN_FAILURE'; payload: { attempts: number } }
  | { type: 'LOGOUT' }
  | { type: 'SESSION_EXPIRED' }
  | { type: 'RESET_ATTEMPTS' };

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
  timestamp: string;
  stack?: string;
}

export interface ValidationError {
  field: string;
  message: string;
  value?: unknown;
}

// API response types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: AppError;
  metadata?: {
    requestId: string;
    timestamp: string;
    version: string;
  };
}

export interface STTResponse {
  transcript: string;
  confidence: number;
  language?: string;
  duration: number;
  words?: Array<{
    word: string;
    start: number;
    end: number;
    confidence: number;
  }>;
}

export interface LLMResponse {
  text: string;
  model: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  finishReason: 'stop' | 'length' | 'content_filter' | 'tool_calls';
  toolCalls?: ToolCall[];
}

export interface TTSResponse {
  audioBlob: Blob;
  duration: number;
  format: string;
  sampleRate: number;
}

// Component prop types
export interface AudioRecorderProps {
  isRecording: boolean;
  onStartRecording: () => void;
  onStopRecording: (audioBlob: Blob) => void;
  processingStage: ProcessingStage;
  config?: AudioConfig;
}

export interface ProcessingPipelineProps {
  stage: ProcessingStage;
  currentSession: string | null;
  metrics?: ProcessingMetrics;
}

export interface LogViewerProps {
  sessionLogs: SessionLog[];
  currentSession: string | null;
  onSessionSelect?: (session: SessionLog) => void;
}

export interface ConfigPanelProps {
  systemPrompt: string;
  onSystemPromptChange: (prompt: string) => void;
  selectedVoice: string;
  onVoiceChange: (voice: string) => void;
  config?: AppConfiguration;
}

export interface STTTesterProps {
  onTranscriptGenerated?: (transcript: string, metrics: ProcessingMetrics) => void;
}

export interface LLMTesterProps {
  tools?: Tool[];
  onResponseGenerated?: (response: string, toolCalls: ToolCall[]) => void;
}

export interface TTSTesterProps {
  onAudioGenerated?: (audioBlob: Blob, metrics: ProcessingMetrics) => void;
}

export interface ToolsManagerProps {
  tools: Tool[];
  onToolsChange: (tools: Tool[]) => void;
}

// State management types
export interface AppState {
  session: {
    current: string | null;
    logs: SessionLog[];
    isRecording: boolean;
    processingStage: ProcessingStage;
  };
  configuration: AppConfiguration;
  tools: Tool[];
  ui: {
    activeTab: string;
    isConfigDialogOpen: boolean;
    errors: AppError[];
  };
}

export type AppAction =
  | { type: 'SESSION_START'; payload: { sessionId: string } }
  | { type: 'SESSION_END'; payload: { sessionLog: SessionLog } }
  | { type: 'RECORDING_START' }
  | { type: 'RECORDING_STOP'; payload: { audioBlob: Blob } }
  | { type: 'PROCESSING_STAGE_CHANGE'; payload: { stage: ProcessingStage } }
  | { type: 'CONFIG_UPDATE'; payload: { config: Partial<AppConfiguration> } }
  | { type: 'TOOLS_UPDATE'; payload: { tools: Tool[] } }
  | { type: 'ERROR_ADD'; payload: { error: AppError } }
  | { type: 'ERROR_CLEAR'; payload: { errorId?: string } }
  | { type: 'UI_TAB_CHANGE'; payload: { tab: string } };

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// Event types
export interface AudioRecordingEvent {
  type: 'start' | 'stop' | 'error';
  timestamp: string;
  data?: unknown;
}

export interface ProcessingEvent {
  type: 'stage_change' | 'progress' | 'complete' | 'error';
  stage: ProcessingStage;
  progress?: number;
  data?: unknown;
  error?: AppError;
}

// Hook types
export interface UseAudioRecorderReturn {
  isRecording: boolean;
  audioLevel: number;
  duration: number;
  startRecording: () => Promise<void>;
  stopRecording: () => void;
  audioBlob: Blob | null;
  error: string | null;
}

export interface UseConfigurationReturn {
  config: AppConfiguration;
  updateConfig: (updates: DeepPartial<AppConfiguration>) => void;
  isConfigured: boolean;
  validateConfig: () => ValidationError[];
}

export interface UseToolsReturn {
  tools: Tool[];
  addTool: (tool: Omit<Tool, 'id'>) => void;
  updateTool: (id: string, updates: Partial<Tool>) => void;
  deleteTool: (id: string) => void;
  executeTool: (toolId: string, parameters: Record<string, unknown>) => Promise<ToolCall>;
}
