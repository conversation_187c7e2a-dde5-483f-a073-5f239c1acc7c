import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const SystemPromptEditor: React.FC = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">System Prompt Editor</h1>
        <p className="text-muted-foreground">
          Configure AI behavior and response patterns
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Coming Soon</CardTitle>
          <CardDescription>
            This feature will be implemented in the next phase
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            The System Prompt Editor will allow you to:
          </p>
          <ul className="list-disc list-inside mt-2 space-y-1 text-muted-foreground">
            <li>Edit system prompts for different AI models</li>
            <li>Create and manage prompt templates</li>
            <li>Version control for prompt changes</li>
            <li>Test prompts before deployment</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};

export default SystemPromptEditor;
