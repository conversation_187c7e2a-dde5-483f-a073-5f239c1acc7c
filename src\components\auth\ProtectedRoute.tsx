import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { Loader2 } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAdmin?: boolean;
  fallbackPath?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requireAdmin = true,
  fallbackPath = '/admin/login'
}) => {
  const { state } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (state.isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-muted-foreground">Checking authentication...</p>
        </div>
      </div>
    );
  }

  // Check if user is authenticated
  if (!state.isAuthenticated) {
    // Save the attempted location for redirect after login
    return (
      <Navigate 
        to={fallbackPath} 
        state={{ from: location.pathname }} 
        replace 
      />
    );
  }

  // Check admin requirement
  if (requireAdmin && state.user?.role !== 'admin') {
    return (
      <Navigate 
        to="/" 
        replace 
      />
    );
  }

  // Check session validity
  if (state.sessionExpiry && new Date().getTime() >= new Date(state.sessionExpiry).getTime()) {
    return (
      <Navigate 
        to={fallbackPath} 
        state={{ from: location.pathname, expired: true }} 
        replace 
      />
    );
  }

  return <>{children}</>;
};

export default ProtectedRoute;
