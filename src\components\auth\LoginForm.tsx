import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { Eye, EyeOff, Lock, User, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/context/AuthContext';
import type { LoginCredentials } from '@/types';

interface LoginFormProps {
  onSuccess?: () => void;
  redirectTo?: string;
}

const LoginForm: React.FC<LoginFormProps> = ({ onSuccess, redirectTo }) => {
  const { state, actions } = useAuth();
  const [showPassword, setShowPassword] = useState(false);
  const [loginError, setLoginError] = useState<string>('');
  const [lockoutTime, setLockoutTime] = useState<number>(0);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<LoginCredentials>();

  // Handle lockout timer
  useEffect(() => {
    if (state.loginAttempts >= 5) {
      setLockoutTime(300); // 5 minutes lockout
      const timer = setInterval(() => {
        setLockoutTime((prev) => {
          if (prev <= 1) {
            actions.resetLoginAttempts();
            setLoginError('');
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [state.loginAttempts, actions]);

  const onSubmit = async (data: LoginCredentials) => {
    if (lockoutTime > 0) return;

    setLoginError('');
    
    try {
      const success = await actions.login(data);
      
      if (success) {
        reset();
        onSuccess?.();
      } else {
        if (state.loginAttempts >= 4) {
          setLoginError('Too many failed attempts. Account locked for 5 minutes.');
        } else {
          setLoginError(`Invalid credentials. ${5 - state.loginAttempts - 1} attempts remaining.`);
        }
      }
    } catch (error) {
      setLoginError('Login failed. Please try again.');
    }
  };

  const formatLockoutTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const isLocked = lockoutTime > 0;

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="flex items-center justify-center mb-4">
            <div className="p-3 bg-primary/10 rounded-full">
              <Lock className="h-6 w-6 text-primary" />
            </div>
          </div>
          <CardTitle className="text-2xl text-center">Developer Access</CardTitle>
          <CardDescription className="text-center">
            Enter your credentials to access the admin panel
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="username">Username</Label>
              <div className="relative">
                <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="username"
                  type="text"
                  placeholder="Enter username"
                  className="pl-10"
                  disabled={state.isLoading || isLocked}
                  {...register('username', {
                    required: 'Username is required',
                    minLength: {
                      value: 1,
                      message: 'Username must be at least 1 character',
                    },
                  })}
                />
              </div>
              {errors.username && (
                <p className="text-sm text-destructive">{errors.username.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Enter password"
                  className="pl-10 pr-10"
                  disabled={state.isLoading || isLocked}
                  {...register('password', {
                    required: 'Password is required',
                    minLength: {
                      value: 1,
                      message: 'Password is required',
                    },
                  })}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={state.isLoading || isLocked}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <Eye className="h-4 w-4 text-muted-foreground" />
                  )}
                </Button>
              </div>
              {errors.password && (
                <p className="text-sm text-destructive">{errors.password.message}</p>
              )}
            </div>

            {(loginError || isLocked) && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  {isLocked 
                    ? `Account locked. Try again in ${formatLockoutTime(lockoutTime)}`
                    : loginError
                  }
                </AlertDescription>
              </Alert>
            )}

            <Button
              type="submit"
              className="w-full"
              disabled={state.isLoading || isLocked}
            >
              {state.isLoading ? 'Signing in...' : 'Sign In'}
            </Button>
          </form>

          {state.loginAttempts > 0 && state.loginAttempts < 5 && (
            <div className="mt-4 text-center">
              <p className="text-sm text-muted-foreground">
                Failed attempts: {state.loginAttempts}/5
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default LoginForm;
