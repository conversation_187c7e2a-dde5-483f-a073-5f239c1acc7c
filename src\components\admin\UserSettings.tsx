import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const UserSettings: React.FC = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">User Settings</h1>
        <p className="text-muted-foreground">
          Manage admin account and preferences
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Coming Soon</CardTitle>
          <CardDescription>
            This feature will be implemented in the next phase
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            User Settings will allow you to:
          </p>
          <ul className="list-disc list-inside mt-2 space-y-1 text-muted-foreground">
            <li>Change admin password</li>
            <li>Configure session timeout settings</li>
            <li>Set notification preferences</li>
            <li>Manage security settings</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};

export default UserSettings;
