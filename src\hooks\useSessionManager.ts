import { useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useAppContext } from '@/context/AppContext';
import LoggingService from '@/services/loggingService';
import type { SessionLog, ProcessingMetrics } from '@/types';

interface UseSessionManagerReturn {
  currentSession: string | null;
  sessionLogs: SessionLog[];
  isRecording: boolean;
  processingStage: string;
  startSession: () => Promise<string>;
  endSession: (sessionData: Partial<SessionLog>) => Promise<void>;
  updateSessionStage: (stage: string) => void;
  simulateProcessing: (audioBlob: Blob) => Promise<void>;
}

export const useSessionManager = (): UseSessionManagerReturn => {
  const { state, actions } = useAppContext();
  const { toast } = useToast();
  const loggingService = LoggingService.getInstance();

  const startSession = useCallback(async (): Promise<string> => {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    actions.startSession(sessionId);

    // Create session log in persistent storage
    try {
      await loggingService.createSessionLog({
        sessionId,
        sessionType: 'full_pipeline',
        systemPrompt: "You are an AI voice assistant. Respond naturally and concisely to the user's questions."
      });
    } catch (error) {
      console.error('Failed to create session log:', error);
    }

    toast({
      title: "Session Started",
      description: `New session: ${sessionId.split('_')[1]}`
    });

    return sessionId;
  }, [actions, toast, loggingService]);

  const endSession = useCallback(async (sessionData: Partial<SessionLog>) => {
    if (!state.session.current) return;

    const sessionLog: SessionLog = {
      id: state.session.current,
      timestamp: new Date().toISOString(),
      inputAudio: null,
      transcript: '',
      gptResponse: '',
      ttsAudio: null,
      status: 'completed',
      ...sessionData
    };

    // Update persistent storage
    try {
      await loggingService.updateSessionLog(state.session.current, {
        status: sessionData.status as any || 'completed',
        sttTranscript: sessionData.transcript,
        llmResponse: sessionData.gptResponse,
        processingMetrics: sessionData.metrics
      });
    } catch (error) {
      console.error('Failed to update session log:', error);
    }

    actions.endSession(sessionLog);

    toast({
      title: "Session Complete",
      description: "Session data has been saved"
    });
  }, [state.session.current, actions, toast, loggingService]);

  const updateSessionStage = useCallback((stage: string) => {
    actions.setProcessingStage(stage as any);
  }, [actions]);

  const simulateProcessing = useCallback(async (audioBlob: Blob): Promise<void> => {
    if (!state.session.current) return;

    try {
      // Update session with input audio
      await loggingService.updateSessionLog(state.session.current, {
        status: 'processing'
      });

      // Simulate STT processing
      actions.setProcessingStage('stt');
      await new Promise(resolve => setTimeout(resolve, 1500));

      const transcript = "This is a sample transcript from the STT service. The audio has been processed successfully.";
      await loggingService.updateSessionLog(state.session.current, {
        sttTranscript: transcript,
        sttProvider: 'deepgram',
        sttModel: 'nova-2',
        sttConfidence: 0.95,
        sttProcessingTime: 1500
      });

      // Simulate LLM processing
      actions.setProcessingStage('llm');
      await new Promise(resolve => setTimeout(resolve, 2000));

      const llmResponse = "Thank you for testing the voice AI pipeline. This is a simulated response from the language model that would normally process your speech and generate an appropriate reply.";
      await loggingService.updateSessionLog(state.session.current, {
        llmResponse,
        llmProvider: 'openai',
        llmModel: 'gpt-4',
        llmProcessingTime: 2000,
        llmTokensUsed: 150
      });

      // Simulate TTS processing
      actions.setProcessingStage('tts');
      await new Promise(resolve => setTimeout(resolve, 1500));

      await loggingService.updateSessionLog(state.session.current, {
        ttsProvider: 'elevenlabs',
        ttsModel: 'eleven_multilingual_v2',
        ttsVoice: 'Aria',
        ttsProcessingTime: 1500
      });

      // Create mock session data
      const mockMetrics: ProcessingMetrics = {
        sttLatency: 1.5,
        llmProcessing: 2.0,
        ttsGeneration: 1.5,
        totalTime: 5.0
      };

      const sessionData: Partial<SessionLog> = {
        inputAudio: audioBlob,
        transcript,
        gptResponse: llmResponse,
        ttsAudio: null, // In a real implementation, this would be the generated audio
        status: 'completed',
        metrics: mockMetrics
      };

      await endSession(sessionData);
      actions.setProcessingStage('completed');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Processing failed';

      // Update session with error
      if (state.session.current) {
        await loggingService.updateSessionLog(state.session.current, {
          status: 'error',
          errorMessage
        });
      }

      actions.addError({
        code: 'PROCESSING_ERROR',
        message: errorMessage,
        timestamp: new Date().toISOString()
      });

      actions.setProcessingStage('error');

      toast({
        title: "Processing Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  }, [state.session.current, actions, endSession, toast, loggingService]);

  return {
    currentSession: state.session.current,
    sessionLogs: state.session.logs,
    isRecording: state.session.isRecording,
    processingStage: state.session.processingStage,
    startSession,
    endSession,
    updateSessionStage,
    simulateProcessing
  };
};
