import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Clock, 
  Mic, 
  Brain, 
  Volume2, 
  Zap,
  ExternalLink,
  AlertTriangle,
  Share2,
  Download
} from 'lucide-react';
import { supabase, type SessionLogRecord } from '@/lib/supabase';

const ShareSession: React.FC = () => {
  const { shareToken } = useParams<{ shareToken: string }>();
  const [session, setSession] = useState<SessionLogRecord | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadSharedSession = async () => {
      if (!shareToken) {
        setError('Invalid share link');
        setIsLoading(false);
        return;
      }

      try {
        const { data, error } = await supabase
          .from('session_logs')
          .select('*')
          .eq('share_token', shareToken)
          .single();

        if (error) {
          if (error.code === 'PGRST116') {
            setError('Session not found or link has expired');
          } else {
            setError('Failed to load session');
          }
          setIsLoading(false);
          return;
        }

        // Check if link has expired
        if (data.share_expires_at && new Date(data.share_expires_at) < new Date()) {
          setError('This share link has expired');
          setIsLoading(false);
          return;
        }

        setSession(data);
      } catch (err) {
        console.error('Error loading shared session:', err);
        setError('Failed to load session');
      } finally {
        setIsLoading(false);
      }
    };

    loadSharedSession();
  }, [shareToken]);

  const getSessionTypeIcon = (sessionType: string) => {
    switch (sessionType) {
      case 'full_pipeline': return <Zap className="w-5 h-5" />;
      case 'stt_test': return <Mic className="w-5 h-5" />;
      case 'llm_test': return <Brain className="w-5 h-5" />;
      case 'tts_test': return <Volume2 className="w-5 h-5" />;
      default: return <Clock className="w-5 h-5" />;
    }
  };

  const getSessionTypeName = (sessionType: string) => {
    switch (sessionType) {
      case 'full_pipeline': return 'Full Pipeline Test';
      case 'stt_test': return 'Speech-to-Text Test';
      case 'llm_test': return 'Language Model Test';
      case 'tts_test': return 'Text-to-Speech Test';
      default: return 'Voice AI Test';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500';
      case 'error': return 'bg-red-500';
      case 'processing': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  const copyShareLink = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      // You could add a toast notification here
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 p-4">
        <div className="max-w-4xl mx-auto pt-20">
          <Card>
            <CardContent className="p-8 text-center">
              <div className="animate-spin w-8 h-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-4" />
              <div className="text-muted-foreground">Loading shared session...</div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error || !session) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 p-4">
        <div className="max-w-4xl mx-auto pt-20">
          <Card>
            <CardContent className="p-8 text-center">
              <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
              <h2 className="text-xl font-semibold mb-2">Unable to Load Session</h2>
              <p className="text-muted-foreground mb-6">{error}</p>
              <Button asChild>
                <Link to="/">
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Go to Voice AI Sandbox
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 p-4">
      <div className="max-w-4xl mx-auto pt-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl text-white">
              {getSessionTypeIcon(session.session_type)}
            </div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
              Shared Session
            </h1>
          </div>
          <p className="text-muted-foreground">
            {getSessionTypeName(session.session_type)} • {new Date(session.created_at).toLocaleString()}
          </p>
        </div>

        {/* Session Details */}
        <div className="grid gap-6 md:grid-cols-2">
          {/* Session Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5" />
                Session Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Status</span>
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${getStatusColor(session.status)}`} />
                  <Badge variant="outline">{session.status.toUpperCase()}</Badge>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Type</span>
                <Badge variant="secondary">{getSessionTypeName(session.session_type)}</Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Created</span>
                <span className="text-sm text-muted-foreground">
                  {new Date(session.created_at).toLocaleString()}
                </span>
              </div>

              {session.stt_processing_time && session.llm_processing_time && session.tts_processing_time && (
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Total Duration</span>
                  <span className="text-sm text-muted-foreground">
                    {((session.stt_processing_time + session.llm_processing_time + session.tts_processing_time) / 1000).toFixed(1)}s
                  </span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Processing Results */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="w-5 h-5" />
                Processing Results
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {session.stt_transcript && (
                <div>
                  <div className="text-sm font-medium mb-1">Speech-to-Text</div>
                  <div className="text-sm text-muted-foreground bg-muted p-2 rounded">
                    "{session.stt_transcript}"
                  </div>
                </div>
              )}

              {session.llm_response && (
                <div>
                  <div className="text-sm font-medium mb-1">AI Response</div>
                  <div className="text-sm text-muted-foreground bg-muted p-2 rounded">
                    "{session.llm_response}"
                  </div>
                </div>
              )}

              {session.error_message && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    {session.error_message}
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Actions */}
        <div className="mt-8 flex justify-center gap-4">
          <Button onClick={copyShareLink} variant="outline">
            <Share2 className="w-4 h-4 mr-2" />
            Copy Link
          </Button>
          
          <Button asChild>
            <Link to="/">
              <ExternalLink className="w-4 h-4 mr-2" />
              Try Voice AI Sandbox
            </Link>
          </Button>
        </div>

        {/* Footer */}
        <div className="mt-12 text-center text-sm text-muted-foreground">
          <p>Powered by Voice AI Sandbox</p>
          {session.share_expires_at && (
            <p className="mt-1">
              This link expires on {new Date(session.share_expires_at).toLocaleString()}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default ShareSession;
