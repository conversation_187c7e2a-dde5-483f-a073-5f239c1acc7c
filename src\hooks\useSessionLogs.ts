import { useState, useEffect, useCallback } from 'react';
import LoggingService, { type SessionLogRecord } from '@/services/loggingService';

interface UseSessionLogsReturn {
  sessionLogs: SessionLogRecord[];
  isLoading: boolean;
  error: string | null;
  refreshLogs: () => Promise<void>;
  generateShareLink: (sessionId: string) => Promise<string>;
  setSessionVisibility: (sessionId: string, isVisible: boolean) => Promise<void>;
}

export const useSessionLogs = (includeHidden = false): UseSessionLogsReturn => {
  const [sessionLogs, setSessionLogs] = useState<SessionLogRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const loggingService = LoggingService.getInstance();

  const loadSessionLogs = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const logs = includeHidden 
        ? await loggingService.getAllSessionLogs(true)
        : await loggingService.getSessionLogs(50, 0);
      
      setSessionLogs(logs);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load session logs';
      setError(errorMessage);
      console.error('Failed to load session logs:', err);
    } finally {
      setIsLoading(false);
    }
  }, [includeHidden, loggingService]);

  const refreshLogs = useCallback(async () => {
    await loadSessionLogs();
  }, [loadSessionLogs]);

  const generateShareLink = useCallback(async (sessionId: string): Promise<string> => {
    try {
      return await loggingService.generateShareLink(sessionId, {
        expiresInHours: 24,
        includeAudio: true
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate share link';
      throw new Error(errorMessage);
    }
  }, [loggingService]);

  const setSessionVisibility = useCallback(async (sessionId: string, isVisible: boolean): Promise<void> => {
    try {
      await loggingService.setSessionVisibility(sessionId, isVisible);
      // Refresh logs to reflect the change
      await refreshLogs();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update session visibility';
      throw new Error(errorMessage);
    }
  }, [loggingService, refreshLogs]);

  // Load logs on mount
  useEffect(() => {
    loadSessionLogs();
  }, [loadSessionLogs]);

  return {
    sessionLogs,
    isLoading,
    error,
    refreshLogs,
    generateShareLink,
    setSessionVisibility,
  };
};
