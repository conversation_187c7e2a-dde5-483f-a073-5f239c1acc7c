import { Handler, HandlerEvent, HandlerContext } from '@netlify/functions';

const handler: Handler = async (event: HandlerEvent, context: HandlerContext) => {
  // Enable CORS
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Content-Type': 'application/json',
  };

  // <PERSON>le preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: '',
    };
  }

  if (event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' }),
    };
  }

  try {
    // Check if required environment variables are present
    const requiredEnvVars = [
      'DEEPGRAM_API_KEY',
      'OPENAI_API_KEY',
      'ELEVENLABS_API_KEY'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'unknown',
      functions: {
        deepgram: !!process.env.DEEPGRAM_API_KEY,
        openai: !!process.env.OPENAI_API_KEY,
        elevenlabs: !!process.env.ELEVENLABS_API_KEY,
        google: !!process.env.GOOGLE_CLOUD_API_KEY,
        anthropic: !!process.env.ANTHROPIC_API_KEY,
        azure: !!process.env.AZURE_SPEECH_API_KEY,
        assemblyai: !!process.env.ASSEMBLYAI_API_KEY,
      },
      missingApiKeys: missingVars,
      version: '1.0.0'
    };

    // If critical API keys are missing, mark as unhealthy
    if (missingVars.length > 0) {
      healthStatus.status = 'degraded';
    }

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify(healthStatus),
    };

  } catch (error) {
    console.error('Health check error:', error);
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ 
        status: 'unhealthy',
        error: 'Health check failed',
        timestamp: new Date().toISOString()
      }),
    };
  }
};

export { handler };
