# Voice AI Sandbox - Deployment Guide

This guide covers the complete deployment process for the two-tier Voice AI Sandbox with authentication, persistent logging, and Supabase integration.

## 🏗️ Architecture Overview

The application now features:

### **Public End-User Interface** (`/`)
- Clean, user-friendly interface with 5 main tabs:
  - **Full Pipeline**: Complete STT → LLM → TTS workflow testing
  - **STT Testing**: Speech-to-text testing with multiple providers
  - **LLM Testing**: Language model testing with multiple providers  
  - **TTS Testing**: Text-to-speech testing with multiple providers
  - **Session Logs**: View and listen to all test session history
- No access to system prompts, tools, or configuration
- Can view public session logs and generate shareable links

### **Protected Developer Interface** (`/admin/*`)
- Secure admin control panel with authentication
- Username: `22` / Password: `I'mTheHitman!`
- Full control over:
  - System prompt editing
  - Tool configuration
  - Provider/model management
  - Session visibility controls
  - Advanced settings and analytics

### **Persistent Logging System**
- Supabase backend for session persistence
- Shareable links for individual sessions (`/share/:token`)
- Admin controls to hide/show sessions from public view
- Fallback to localStorage when Supa<PERSON> is unavailable

## 🚀 Deployment Steps

### 1. Supabase Setup

1. **Create Supabase Project**
   ```bash
   # Go to https://supabase.com and create a new project
   # Note down your project URL and API keys
   ```

2. **Run Database Schema**
   - Go to your Supabase dashboard → SQL Editor
   - Copy and run the SQL from `src/lib/supabase.ts` (the `createTablesSQL` constant)
   - This creates tables for session logs, user preferences, and system configuration

3. **Configure Storage (Optional)**
   ```sql
   -- Create storage bucket for audio files
   INSERT INTO storage.buckets (id, name, public) VALUES ('audio-files', 'audio-files', true);
   
   -- Create policy for public access
   CREATE POLICY "Public Access" ON storage.objects FOR SELECT USING (bucket_id = 'audio-files');
   CREATE POLICY "Authenticated Upload" ON storage.objects FOR INSERT WITH CHECK (bucket_id = 'audio-files');
   ```

### 2. Netlify Deployment

1. **Environment Variables**
   Set these in your Netlify dashboard (Site Settings → Environment Variables):

   ```bash
   # API Keys (server-side only)
   DEEPGRAM_API_KEY=your_deepgram_key
   OPENAI_API_KEY=your_openai_key
   ELEVENLABS_API_KEY=your_elevenlabs_key
   ANTHROPIC_API_KEY=your_anthropic_key
   GOOGLE_CLOUD_API_KEY=your_google_key
   AZURE_SPEECH_API_KEY=your_azure_key
   
   # Supabase Configuration (client-side safe)
   VITE_SUPABASE_URL=https://your-project.supabase.co
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   
   # Supabase Service Role (server-side only)
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   
   # Application Settings
   VITE_ENVIRONMENT=production
   VITE_API_BASE_URL=/.netlify/functions
   ```

2. **Deploy to Netlify**
   ```bash
   # Build and deploy
   npm run build
   npm run deploy
   
   # Or connect your GitHub repo for automatic deployments
   ```

### 3. Local Development Setup

1. **Clone and Install**
   ```bash
   git clone <your-repo-url>
   cd voice-ai-sandbox
   npm install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your API keys and Supabase configuration
   ```

3. **Start Development Server**
   ```bash
   npm run dev
   ```

## 🔐 Authentication & Security

### Admin Access
- **URL**: `/admin/login`
- **Username**: `22`
- **Password**: `I'mTheHitman!`
- **Session Duration**: 8 hours
- **Lockout**: 5 failed attempts = 5 minute lockout

### Security Features
- Session-based authentication with localStorage persistence
- Route protection for admin areas
- Row Level Security (RLS) on Supabase tables
- CSP headers configured for all required domains
- API keys secured server-side via Netlify Functions

## 📊 Logging & Analytics

### Session Tracking
- All user interactions are logged to Supabase
- Fallback to localStorage when Supabase unavailable
- Detailed metrics for STT, LLM, and TTS processing times
- Audio file storage with public URLs

### Admin Controls
- View all sessions (including hidden ones)
- Toggle session visibility for public users
- Generate shareable links with expiration
- Export session data and analytics

### Shareable Links
- Format: `https://your-domain.com/share/:token`
- Configurable expiration (default: 24 hours)
- Public access without authentication
- Includes full session details and results

## 🛠️ Customization

### Adding New Providers
1. Update API proxy functions in `netlify/functions/`
2. Add provider configuration in admin interface
3. Update model registries and UI components

### Modifying Authentication
- Change credentials in `src/context/AuthContext.tsx`
- Adjust session duration and lockout settings
- Add additional user roles if needed

### Extending Logging
- Modify database schema in `src/lib/supabase.ts`
- Update logging service in `src/services/loggingService.ts`
- Add new metrics and data points as needed

## 🔧 Troubleshooting

### Common Issues

1. **Supabase Connection Failed**
   - Check environment variables are set correctly
   - Verify Supabase project is active and accessible
   - Application will fallback to localStorage automatically

2. **Admin Login Not Working**
   - Verify credentials: username `22`, password `I'mTheHitman!`
   - Check browser localStorage for session data
   - Clear localStorage and try again

3. **API Functions Failing**
   - Check Netlify function logs
   - Verify API keys are set in Netlify environment variables
   - Test functions individually via Netlify dashboard

4. **Build Errors**
   - Run `npm run build` locally to test
   - Check for TypeScript errors
   - Verify all dependencies are installed

### Performance Optimization

1. **Code Splitting**
   ```javascript
   // Consider lazy loading admin components
   const AdminDashboard = lazy(() => import('./pages/admin/AdminDashboard'));
   ```

2. **Database Optimization**
   ```sql
   -- Add indexes for better query performance
   CREATE INDEX idx_session_logs_user_id ON session_logs(user_id);
   CREATE INDEX idx_session_logs_session_type ON session_logs(session_type);
   ```

## 📈 Monitoring & Maintenance

### Health Checks
- Monitor Supabase dashboard for database health
- Check Netlify function execution logs
- Monitor API usage and rate limits

### Regular Maintenance
- Rotate API keys quarterly
- Clean up expired share tokens
- Archive old session logs
- Update dependencies regularly

### Backup Strategy
- Supabase provides automatic backups
- Export critical session data periodically
- Maintain local backups of configuration

## 🎯 Next Steps

The foundation is now complete! Consider these enhancements:

1. **Real-time Features**: WebSocket connections for live session monitoring
2. **Advanced Analytics**: Charts and graphs for usage patterns
3. **User Management**: Multiple admin users with different permissions
4. **API Rate Limiting**: Implement usage quotas and throttling
5. **Mobile Optimization**: Responsive design improvements
6. **Internationalization**: Multi-language support

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Review Netlify and Supabase documentation
3. Check browser console for client-side errors
4. Review server logs in Netlify dashboard
