import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key';

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database types
export interface SessionLogRecord {
  id: string;
  created_at: string;
  updated_at: string;
  session_id: string;
  user_id?: string;
  session_type: 'full_pipeline' | 'stt_test' | 'llm_test' | 'tts_test';
  status: 'pending' | 'processing' | 'completed' | 'error';
  
  // Input data
  input_audio_url?: string;
  input_text?: string;
  
  // Processing results
  stt_transcript?: string;
  stt_provider?: string;
  stt_model?: string;
  stt_confidence?: number;
  stt_processing_time?: number;
  
  llm_response?: string;
  llm_provider?: string;
  llm_model?: string;
  llm_processing_time?: number;
  llm_tokens_used?: number;
  
  tts_audio_url?: string;
  tts_provider?: string;
  tts_model?: string;
  tts_voice?: string;
  tts_processing_time?: number;
  
  // Metadata
  system_prompt?: string;
  tools_used?: string[];
  error_message?: string;
  processing_metrics?: Record<string, any>;
  
  // Admin controls
  is_visible_to_public: boolean;
  is_featured: boolean;
  admin_notes?: string;
  
  // Sharing
  share_token?: string;
  share_expires_at?: string;
}

export interface UserPreference {
  id: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  preference_key: string;
  preference_value: any;
}

export interface SystemConfiguration {
  id: string;
  created_at: string;
  updated_at: string;
  config_key: string;
  config_value: any;
  is_active: boolean;
  admin_only: boolean;
}

// Database schema creation SQL (for reference)
export const createTablesSQL = `
-- Session logs table
CREATE TABLE IF NOT EXISTS session_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  session_id TEXT NOT NULL,
  user_id TEXT,
  session_type TEXT NOT NULL CHECK (session_type IN ('full_pipeline', 'stt_test', 'llm_test', 'tts_test')),
  status TEXT NOT NULL CHECK (status IN ('pending', 'processing', 'completed', 'error')),
  
  -- Input data
  input_audio_url TEXT,
  input_text TEXT,
  
  -- STT results
  stt_transcript TEXT,
  stt_provider TEXT,
  stt_model TEXT,
  stt_confidence DECIMAL,
  stt_processing_time INTEGER,
  
  -- LLM results
  llm_response TEXT,
  llm_provider TEXT,
  llm_model TEXT,
  llm_processing_time INTEGER,
  llm_tokens_used INTEGER,
  
  -- TTS results
  tts_audio_url TEXT,
  tts_provider TEXT,
  tts_model TEXT,
  tts_voice TEXT,
  tts_processing_time INTEGER,
  
  -- Metadata
  system_prompt TEXT,
  tools_used TEXT[],
  error_message TEXT,
  processing_metrics JSONB,
  
  -- Admin controls
  is_visible_to_public BOOLEAN DEFAULT true,
  is_featured BOOLEAN DEFAULT false,
  admin_notes TEXT,
  
  -- Sharing
  share_token TEXT UNIQUE,
  share_expires_at TIMESTAMP WITH TIME ZONE
);

-- User preferences table
CREATE TABLE IF NOT EXISTS user_preferences (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  user_id TEXT NOT NULL,
  preference_key TEXT NOT NULL,
  preference_value JSONB,
  UNIQUE(user_id, preference_key)
);

-- System configuration table
CREATE TABLE IF NOT EXISTS system_configurations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  config_key TEXT NOT NULL UNIQUE,
  config_value JSONB,
  is_active BOOLEAN DEFAULT true,
  admin_only BOOLEAN DEFAULT false
);

-- Indexes
CREATE INDEX IF NOT EXISTS idx_session_logs_session_id ON session_logs(session_id);
CREATE INDEX IF NOT EXISTS idx_session_logs_created_at ON session_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_session_logs_status ON session_logs(status);
CREATE INDEX IF NOT EXISTS idx_session_logs_visible ON session_logs(is_visible_to_public);
CREATE INDEX IF NOT EXISTS idx_session_logs_share_token ON session_logs(share_token);
CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_system_configurations_key ON system_configurations(config_key);

-- Row Level Security (RLS)
ALTER TABLE session_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_configurations ENABLE ROW LEVEL SECURITY;

-- Policies
CREATE POLICY "Public can view visible session logs" ON session_logs
  FOR SELECT USING (is_visible_to_public = true);

CREATE POLICY "Anyone can insert session logs" ON session_logs
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can view their own preferences" ON user_preferences
  FOR ALL USING (user_id = current_setting('app.current_user_id', true));

CREATE POLICY "Public can view non-admin configurations" ON system_configurations
  FOR SELECT USING (admin_only = false AND is_active = true);

-- Functions
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers
CREATE TRIGGER update_session_logs_updated_at BEFORE UPDATE ON session_logs
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_preferences_updated_at BEFORE UPDATE ON user_preferences
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_configurations_updated_at BEFORE UPDATE ON system_configurations
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
`;

export default supabase;
