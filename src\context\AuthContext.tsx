import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import type { AuthState, AuthAction, AuthUser, LoginCredentials } from '@/types';

// Initial state
const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  sessionExpiry: null,
  loginAttempts: 0,
  isLoading: true, // Start with loading to check existing session
};

// Auth reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN_START':
      return {
        ...state,
        isLoading: true,
      };

    case 'LOGIN_SUCCESS':
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload.user,
        sessionExpiry: action.payload.sessionExpiry,
        loginAttempts: 0,
        isLoading: false,
      };

    case 'LOGIN_FAILURE':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        sessionExpiry: null,
        loginAttempts: action.payload.attempts,
        isLoading: false,
      };

    case 'LOGOUT':
    case 'SESSION_EXPIRED':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        sessionExpiry: null,
        isLoading: false,
      };

    case 'RESET_ATTEMPTS':
      return {
        ...state,
        loginAttempts: 0,
      };

    default:
      return state;
  }
};

// Context type
interface AuthContextType {
  state: AuthState;
  dispatch: React.Dispatch<AuthAction>;
  actions: {
    login: (credentials: LoginCredentials) => Promise<boolean>;
    logout: () => void;
    checkSession: () => boolean;
    resetLoginAttempts: () => void;
  };
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Constants
const ADMIN_USERNAME = '22';
const ADMIN_PASSWORD = "I'mTheHitman!";
const SESSION_DURATION = 8 * 60 * 60 * 1000; // 8 hours in milliseconds
const MAX_LOGIN_ATTEMPTS = 5;
const STORAGE_KEY = 'voice_ai_auth_session';

// Provider component
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check for existing session on mount
  useEffect(() => {
    const checkExistingSession = () => {
      try {
        const sessionData = localStorage.getItem(STORAGE_KEY);
        if (sessionData) {
          const { user, sessionExpiry } = JSON.parse(sessionData);
          
          if (new Date().getTime() < new Date(sessionExpiry).getTime()) {
            // Session is still valid
            dispatch({
              type: 'LOGIN_SUCCESS',
              payload: { user, sessionExpiry }
            });
            return;
          } else {
            // Session expired
            localStorage.removeItem(STORAGE_KEY);
            dispatch({ type: 'SESSION_EXPIRED' });
          }
        }
      } catch (error) {
        console.error('Error checking session:', error);
        localStorage.removeItem(STORAGE_KEY);
      }
      
      // No valid session found
      dispatch({ type: 'LOGOUT' });
    };

    checkExistingSession();
  }, []);

  // Session expiry check
  useEffect(() => {
    if (!state.isAuthenticated || !state.sessionExpiry) return;

    const checkExpiry = () => {
      if (new Date().getTime() >= new Date(state.sessionExpiry!).getTime()) {
        localStorage.removeItem(STORAGE_KEY);
        dispatch({ type: 'SESSION_EXPIRED' });
      }
    };

    const interval = setInterval(checkExpiry, 60000); // Check every minute
    return () => clearInterval(interval);
  }, [state.isAuthenticated, state.sessionExpiry]);

  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    dispatch({ type: 'LOGIN_START' });

    // Check if too many attempts
    if (state.loginAttempts >= MAX_LOGIN_ATTEMPTS) {
      dispatch({ 
        type: 'LOGIN_FAILURE', 
        payload: { attempts: state.loginAttempts } 
      });
      return false;
    }

    // Simulate network delay for security
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Validate credentials
    if (credentials.username === ADMIN_USERNAME && credentials.password === ADMIN_PASSWORD) {
      const user: AuthUser = {
        username: credentials.username,
        role: 'admin',
        loginTime: new Date().toISOString(),
      };

      const sessionExpiry = new Date(Date.now() + SESSION_DURATION).toISOString();

      // Store session
      localStorage.setItem(STORAGE_KEY, JSON.stringify({ user, sessionExpiry }));

      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: { user, sessionExpiry }
      });

      return true;
    } else {
      const newAttempts = state.loginAttempts + 1;
      dispatch({ 
        type: 'LOGIN_FAILURE', 
        payload: { attempts: newAttempts } 
      });
      return false;
    }
  };

  const logout = () => {
    localStorage.removeItem(STORAGE_KEY);
    dispatch({ type: 'LOGOUT' });
  };

  const checkSession = (): boolean => {
    if (!state.isAuthenticated || !state.sessionExpiry) return false;
    return new Date().getTime() < new Date(state.sessionExpiry).getTime();
  };

  const resetLoginAttempts = () => {
    dispatch({ type: 'RESET_ATTEMPTS' });
  };

  const contextValue: AuthContextType = {
    state,
    dispatch,
    actions: {
      login,
      logout,
      checkSession,
      resetLoginAttempts,
    },
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
