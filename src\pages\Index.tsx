
import React, { useState, useCallback } from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  Mic,
  FileText,
  Brain,
  Volume2,
  Zap,
  History,
  Settings,
  Shield,
  ExternalLink
} from 'lucide-react';
import AudioRecorder from '@/components/AudioRecorder';
import ProcessingPipeline from '@/components/ProcessingPipeline';
import LogViewer from '@/components/LogViewer';
import SessionHistory from '@/components/SessionHistory';
import STTTester from '@/components/STTTester';
import LLMTester from '@/components/LLMTester';
import TTSTester from '@/components/TTSTester';
import { useAppContext, useSession, useTools } from '@/context/AppContext';
import { useAudioRecording } from '@/hooks/useAudioRecording';
import { useSessionManager } from '@/hooks/useSessionManager';
import { useAuth } from '@/context/AuthContext';
import type { Tool } from '@/types';

const Index: React.FC = () => {
  const { actions } = useAppContext();
  const { state: authState } = useAuth();
  const tools = useTools();
  const {
    currentSession,
    sessionLogs,
    isRecording,
    processingStage,
    startSession,
    simulateProcessing
  } = useSessionManager();

  // Local UI state - simplified for end users
  const [selectedVoice, setSelectedVoice] = useState<string>('Aria - 9BWtsMINqrJLrRacOk9x');

  const handleRecordingStart = useCallback(() => {
    startSession();
  }, [startSession]);

  const handleRecordingStop = useCallback(async (audioBlob: Blob) => {
    await simulateProcessing(audioBlob);
  }, [simulateProcessing]);

  // Audio recording hook
  const audioRecording = useAudioRecording({
    onRecordingStart: handleRecordingStart,
    onRecordingStop: handleRecordingStop
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="max-w-7xl mx-auto p-6 space-y-8">
        {/* Modern Header */}
        <div className="relative">
          <div className="text-center space-y-4 py-8">
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl">
                <Zap className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
                Voice AI Sandbox
              </h1>
            </div>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Test and explore Speech-to-Text, Language Models, and Text-to-Speech capabilities
            </p>

            {/* Status Badge */}
            <div className="flex justify-center">
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse" />
                System Online
              </Badge>
            </div>
          </div>

          {/* Admin Access - Top Right */}
          {authState.isAuthenticated ? (
            <div className="absolute top-4 right-4">
              <Button asChild variant="outline" size="sm" className="bg-white/80 backdrop-blur-sm">
                <Link to="/admin">
                  <Shield className="w-4 h-4 mr-2" />
                  Admin Panel
                </Link>
              </Button>
            </div>
          ) : (
            <div className="absolute top-4 right-4">
              <Button asChild variant="ghost" size="sm" className="text-gray-500 hover:text-gray-700">
                <Link to="/admin/login">
                  <Settings className="w-4 h-4 mr-2" />
                  Admin
                </Link>
              </Button>
            </div>
          )}
        </div>

        {/* Main Tabs */}
        <Tabs defaultValue="full-pipeline" className="w-full">
          <TabsList className="grid w-full grid-cols-5 bg-white/70 backdrop-blur-sm border border-white/20 shadow-lg rounded-xl p-1">
            <TabsTrigger
              value="full-pipeline"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-500 data-[state=active]:text-white rounded-lg transition-all duration-200"
            >
              <Zap className="w-4 h-4 mr-2" />
              Full Pipeline
            </TabsTrigger>
            <TabsTrigger
              value="stt-test"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-indigo-500 data-[state=active]:text-white rounded-lg transition-all duration-200"
            >
              <Mic className="w-4 h-4 mr-2" />
              STT Testing
            </TabsTrigger>
            <TabsTrigger
              value="llm-test"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-pink-500 data-[state=active]:text-white rounded-lg transition-all duration-200"
            >
              <Brain className="w-4 h-4 mr-2" />
              LLM Testing
            </TabsTrigger>
            <TabsTrigger
              value="tts-test"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-emerald-500 data-[state=active]:text-white rounded-lg transition-all duration-200"
            >
              <Volume2 className="w-4 h-4 mr-2" />
              TTS Testing
            </TabsTrigger>
            <TabsTrigger
              value="session-logs"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-gray-500 data-[state=active]:to-slate-500 data-[state=active]:text-white rounded-lg transition-all duration-200"
            >
              <FileText className="w-4 h-4 mr-2" />
              Session Logs
            </TabsTrigger>
          </TabsList>
          
          <div className="mt-8">
            <TabsContent value="full-pipeline" className="space-y-6">
              {/* Full Pipeline Interface */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Recording Panel */}
                <div className="lg:col-span-1 space-y-4">
                  <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-lg">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Mic className="w-5 h-5 text-blue-500" />
                        Voice Recording
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <AudioRecorder
                        isRecording={audioRecording.isRecording}
                        onStartRecording={audioRecording.startRecording}
                        onStopRecording={audioRecording.stopRecording}
                        processingStage={processingStage}
                      />
                    </CardContent>
                  </Card>

                  {/* Simple Voice Selection */}
                  <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-lg">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Volume2 className="w-5 h-5 text-green-500" />
                        Voice Settings
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Selected Voice</label>
                        <select
                          value={selectedVoice}
                          onChange={(e) => setSelectedVoice(e.target.value)}
                          className="w-full p-2 border rounded-md bg-white"
                        >
                          <option value="Aria - 9BWtsMINqrJLrRacOk9x">Aria (Female)</option>
                          <option value="Rachel - 21m00Tcm4TlvDq8ikWAM">Rachel (Female)</option>
                          <option value="Domi - AZnzlk1XvdvUeBnXmlld">Domi (Female)</option>
                          <option value="Bella - EXAVITQu4vr4xnSDxMaL">Bella (Female)</option>
                        </select>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Processing Pipeline & Results */}
                <div className="lg:col-span-2 space-y-4">
                  <ProcessingPipeline
                    stage={processingStage}
                    currentSession={currentSession}
                  />
                  
                  <Tabs defaultValue="current-logs" className="w-full">
                    <TabsList className="grid w-full grid-cols-2 bg-white/70 backdrop-blur-sm">
                      <TabsTrigger value="current-logs" className="data-[state=active]:bg-white/90">
                        Current Session
                      </TabsTrigger>
                      <TabsTrigger value="history" className="data-[state=active]:bg-white/90">
                        History
                      </TabsTrigger>
                    </TabsList>
                    
                    <TabsContent value="current-logs" className="mt-4">
                      <LogViewer
                        sessionLogs={sessionLogs}
                        currentSession={currentSession}
                      />
                    </TabsContent>
                    
                    <TabsContent value="history" className="mt-4">
                      <SessionHistory
                        sessions={sessionLogs}
                        onSessionSelect={(session) => console.log('Selected session:', session.id)}
                      />
                    </TabsContent>
                  </Tabs>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="stt-test">
              <STTTester />
            </TabsContent>
            
            <TabsContent value="llm-test">
              <LLMTester tools={tools} />
            </TabsContent>
            
            <TabsContent value="tts-test">
              <TTSTester />
            </TabsContent>

            <TabsContent value="session-logs">
              <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <History className="w-5 h-5 text-gray-500" />
                    Session History & Logs
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <SessionHistory
                    sessions={sessionLogs}
                    onSessionSelect={(session) => console.log('Selected session:', session.id)}
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </div>
        </Tabs>

        {/* Enhanced Status Bar */}
        <Card className="bg-white/70 backdrop-blur-sm border-white/20 shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-8">
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 bg-gradient-to-r from-green-400 to-green-500 rounded-full animate-pulse"></div>
                  <span className="font-medium text-gray-700">Deepgram STT</span>
                  <span className="text-sm text-green-600 font-semibold">Ready</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full animate-pulse"></div>
                  <span className="font-medium text-gray-700">OpenAI GPT-4</span>
                  <span className="text-sm text-blue-600 font-semibold">Ready</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 bg-gradient-to-r from-purple-400 to-purple-500 rounded-full animate-pulse"></div>
                  <span className="font-medium text-gray-700">ElevenLabs TTS</span>
                  <span className="text-sm text-purple-600 font-semibold">Ready</span>
                </div>
                {tools.filter(t => t.enabled).length > 0 && (
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-gradient-to-r from-orange-400 to-orange-500 rounded-full animate-pulse"></div>
                    <span className="font-medium text-gray-700">AI Tools</span>
                    <span className="text-sm text-orange-600 font-semibold">{tools.filter(t => t.enabled).length} Active</span>
                  </div>
                )}
              </div>
              <div className="text-gray-500 text-sm">
                <span className="font-medium">Sessions:</span> {sessionLogs.length} | 
                <span className="font-medium ml-2">Last:</span> {sessionLogs[0]?.timestamp ? new Date(sessionLogs[0].timestamp).toLocaleTimeString() : 'None'}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Index;
