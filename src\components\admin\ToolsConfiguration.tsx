import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const ToolsConfiguration: React.FC = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Tools Configuration</h1>
        <p className="text-muted-foreground">
          Manage available functions and capabilities
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Coming Soon</CardTitle>
          <CardDescription>
            This feature will be implemented in the next phase
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Tools Configuration will allow you to:
          </p>
          <ul className="list-disc list-inside mt-2 space-y-1 text-muted-foreground">
            <li>Enable/disable specific AI tools</li>
            <li>Configure tool parameters and settings</li>
            <li>Create custom tool definitions</li>
            <li>Monitor tool usage and performance</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};

export default ToolsConfiguration;
