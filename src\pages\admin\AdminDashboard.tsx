import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import AdminLayout from '@/components/admin/AdminLayout';
import AdminOverview from '@/components/admin/AdminOverview';
import SystemPromptEditor from '@/components/admin/SystemPromptEditor';
import ProviderManagement from '@/components/admin/ProviderManagement';
import ToolsConfiguration from '@/components/admin/ToolsConfiguration';
import SessionManagement from '@/components/admin/SessionManagement';
import UserSettings from '@/components/admin/UserSettings';

const AdminDashboard: React.FC = () => {
  const { state } = useAuth();

  if (!state.isAuthenticated || state.user?.role !== 'admin') {
    return <Navigate to="/admin/login" replace />;
  }

  return (
    <AdminLayout>
      <Routes>
        <Route index element={<AdminOverview />} />
        <Route path="prompts" element={<SystemPromptEditor />} />
        <Route path="providers" element={<ProviderManagement />} />
        <Route path="tools" element={<ToolsConfiguration />} />
        <Route path="sessions" element={<SessionManagement />} />
        <Route path="settings" element={<UserSettings />} />
        <Route path="*" element={<Navigate to="/admin" replace />} />
      </Routes>
    </AdminLayout>
  );
};

export default AdminDashboard;
