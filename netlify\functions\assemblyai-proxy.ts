import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HandlerContext } from '@netlify/functions';

interface AssemblyAIRequest {
  audio: string; // base64 encoded audio or URL
  model?: string;
  language_code?: string;
  speaker_labels?: boolean;
  sentiment_analysis?: boolean;
  entity_detection?: boolean;
  iab_categories?: boolean;
  content_safety?: boolean;
}

const handler: Handler = async (event: HandlerEvent, context: HandlerContext) => {
  // Enable CORS
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Content-Type': 'application/json',
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: '',
    };
  }

  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' }),
    };
  }

  try {
    // Get API key from environment variables
    const apiKey = process.env.ASSEMBLYAI_API_KEY;
    if (!apiKey) {
      throw new Error('AssemblyAI API key not configured');
    }

    const requestBody: AssemblyAIRequest = JSON.parse(event.body || '{}');
    
    if (!requestBody.audio) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Audio data is required' }),
      };
    }

    // Step 1: Upload audio if it's base64 encoded
    let audioUrl = requestBody.audio;
    
    if (requestBody.audio.startsWith('data:') || !requestBody.audio.startsWith('http')) {
      // Upload base64 audio to AssemblyAI
      const audioBuffer = Buffer.from(requestBody.audio.replace(/^data:audio\/[^;]+;base64,/, ''), 'base64');
      
      const uploadResponse = await fetch('https://api.assemblyai.com/v2/upload', {
        method: 'POST',
        headers: {
          'authorization': apiKey,
          'content-type': 'application/octet-stream',
        },
        body: audioBuffer,
      });

      if (!uploadResponse.ok) {
        const errorText = await uploadResponse.text();
        throw new Error(`AssemblyAI upload error: ${uploadResponse.status} - ${errorText}`);
      }

      const uploadResult = await uploadResponse.json();
      audioUrl = uploadResult.upload_url;
    }

    // Step 2: Create transcription job
    const transcriptData = {
      audio_url: audioUrl,
      speech_model: requestBody.model === 'best' ? 'best' : 'nano',
      language_code: requestBody.language_code || 'en',
      speaker_labels: requestBody.speaker_labels || false,
      sentiment_analysis: requestBody.sentiment_analysis || false,
      entity_detection: requestBody.entity_detection || false,
      iab_categories: requestBody.iab_categories || false,
      content_safety: requestBody.content_safety || false,
    };

    const transcriptResponse = await fetch('https://api.assemblyai.com/v2/transcript', {
      method: 'POST',
      headers: {
        'authorization': apiKey,
        'content-type': 'application/json',
      },
      body: JSON.stringify(transcriptData),
    });

    if (!transcriptResponse.ok) {
      const errorText = await transcriptResponse.text();
      throw new Error(`AssemblyAI transcript error: ${transcriptResponse.status} - ${errorText}`);
    }

    const transcriptResult = await transcriptResponse.json();

    // Step 3: Poll for completion (simplified for demo - in production, use webhooks)
    let attempts = 0;
    const maxAttempts = 60; // 5 minutes max
    
    while (attempts < maxAttempts) {
      const statusResponse = await fetch(`https://api.assemblyai.com/v2/transcript/${transcriptResult.id}`, {
        headers: {
          'authorization': apiKey,
        },
      });

      if (!statusResponse.ok) {
        throw new Error(`AssemblyAI status check error: ${statusResponse.status}`);
      }

      const statusResult = await statusResponse.json();

      if (statusResult.status === 'completed') {
        return {
          statusCode: 200,
          headers,
          body: JSON.stringify(statusResult),
        };
      } else if (statusResult.status === 'error') {
        throw new Error(`AssemblyAI transcription failed: ${statusResult.error}`);
      }

      // Wait 5 seconds before next check
      await new Promise(resolve => setTimeout(resolve, 5000));
      attempts++;
    }

    // If we get here, transcription is taking too long
    return {
      statusCode: 202,
      headers,
      body: JSON.stringify({
        message: 'Transcription in progress',
        transcript_id: transcriptResult.id,
        status: 'processing'
      }),
    };

  } catch (error) {
    console.error('AssemblyAI proxy error:', error);
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      }),
    };
  }
};

export { handler };
